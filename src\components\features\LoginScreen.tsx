
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { signInWithGoogle } from '@/services/api';
import { useAuth } from '@/hooks/useAuth';
import { Button } from '@/components/common/Button';
import { LeafIcon } from '@/components/common/icons';

const LoginScreen: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const navigate = useNavigate();
  const { user, loading } = useAuth();

  // Redirection automatique si l'utilisateur est déjà connecté
  useEffect(() => {
    if (!loading && user) {
      console.log("🔄 Redirection automatique vers le dashboard");
      navigate('/', { replace: true });
    }
  }, [user, loading, navigate]);

  const handleGoogleSignIn = async () => {
    console.log("🖱️ Clic sur le bouton de connexion Google");
    setIsLoading(true);
    setError(null);

    try {
      const result = await signInWithGoogle();

      if (result?.success) {
        console.log("✅ Connexion réussie, attente de la redirection...");
        // Attendre un peu pour que l'AuthContext se mette à jour
        setTimeout(() => {
          if (!user) {
            console.log("🔄 Redirection manuelle vers le dashboard");
            navigate('/', { replace: true });
          }
        }, 1000);
      } else if (result?.error) {
        console.error("❌ Erreur de connexion:", result.error);
        setError(result.error);
      }
    } catch (err) {
      console.error("❌ Erreur inattendue:", err);
      setError("Une erreur inattendue s'est produite. Veuillez réessayer.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-[#100f1c] p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
        className="w-full max-w-md text-center"
      >
        <div className="inline-block p-4 bg-gradient-to-r from-[#d385f5] to-[#a364f7] rounded-full mb-6">
          <LeafIcon className="w-16 h-16 text-white" />
        </div>
        <h1 className="text-5xl font-bold text-white mb-2">FloraSynth</h1>
        <p className="text-lg text-[#E0E0E0] mb-8">Votre Assistant IA Personnel pour le Soin des Plantes</p>

        {error && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-4 p-3 bg-red-500/20 border border-red-500/30 rounded-lg text-red-300 text-sm"
          >
            {error}
          </motion.div>
        )}

        <Button
          onClick={handleGoogleSignIn}
          className="w-full max-w-xs mx-auto"
          disabled={isLoading}
        >
          {isLoading ? 'Connexion en cours...' : 'Se connecter avec Google'}
        </Button>
      </motion.div>
    </div>
  );
};

export default LoginScreen;
